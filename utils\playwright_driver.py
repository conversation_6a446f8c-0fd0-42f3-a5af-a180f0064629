import os
import re
import sys
import asyncio
import logging
import random
import time
import requests
from dotenv import load_dotenv

from typing import Dict, List, Optional, Union
from urllib.parse import urlparse
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

# 加载环境变量
load_dotenv('.env.local')

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)
# print(ROOT_DIR)

class ProxyPoolManager:
    """代理池管理器"""
    def __init__(self, pool_size: int=10):
        '''
        每个 proxy item 都是一个 map:
        {"ip":"***************", "port":40025, start_time: time.time(), expire_time: time.time() + ttl}
        '''

        self.pool_size = pool_size

        # 每个 item_map {} 包含 ip, port, start_time, expire_time
        self.cur_proxys = []
        self.cur_index = 0

        # 构建请求参数
        self.ttl = 180 - 10 # 代理有效期 (10s 预留)
        self.url = 'http://api.shenlongip.com/ip'
        self.params = {
            'key': os.getenv('PROXY_API_KEY'),
            'protocol': 2,
            'mr': 1,
            'pattern': 'json',
            'need': 1000,
            'count': 1,
            'sign': os.getenv('PROXY_API_SIGN')
        }

    def _get_new_proxy(self, depth: int = 0) -> Optional[Dict[str, str]]:
        '''从神龙代理获取新的代理'''
        # 1. 获取代理 API 请求
        response = requests.get(
            self.url,
            params=self.params,
            timeout=10
        )

        # 2. 解析代理信息
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 200 and data.get('data'):
                proxy_info = data['data'][0]
                proxy_info['start_time'] = time.time()
                proxy_info['expire_time'] = proxy_info['start_time'] + self.ttl # 180s 后失效
                return proxy_info # {ip:xx, port:xx, start_time: time.time(), expire_time: time.time() + 180}
            
        # 3. 获取失败
        logging.warning(f"获取新 proxy 失败: {response}")
        if depth < 3:
            time.sleep(1)
            return self._get_new_proxy(depth + 1)
        
        return None

    def _refresh_proxys(self):
        '''刷新所有代理（先判断是否需要刷新）'''
        # 1. 当代理池为空时，直接获取最大数量的代理
        if len(self.cur_proxys) == 0:
            self.cur_proxys = [self._get_new_proxy() for _ in range(self.pool_size)]
            self.cur_proxys = [proxy for proxy in self.cur_proxys if proxy]
            return

        # 2. 当代理池不为空时，检查每个代理是否过期（expire_time < time.time()）
        for proxy in self.cur_proxys:
            if proxy['expire_time'] < time.time():
                self.cur_proxys.remove(proxy)
                new_proxy = self._get_new_proxy()
                if new_proxy: # 只添加有效的代理
                    self.cur_proxys.append(new_proxy)

        # 3. 如果代理池中有效数量不足，补充代理
        self.cur_proxys = [proxy for proxy in self.cur_proxys if proxy]
        if len(self.cur_proxys) < self.pool_size:
            needed = self.pool_size - len(self.cur_proxys)
            new_proxys = [self._get_new_proxy() for _ in range(needed)]
            new_proxys = [proxy for proxy in new_proxys if proxy]

            self.cur_proxys.extend(new_proxys)

    def get_proxy(self) -> Optional[Dict[str, str]]:
        """从代理池顺序获取一个 ip"""
        # 1. 刷新代理池
        self._refresh_proxys()
        if len(self.cur_proxys) == 0: return None

        # 2. 获取代理
        self.cur_index = self.cur_index % len(self.cur_proxys)
        proxy = self.cur_proxys[self.cur_index]
        self.cur_index += 1

        return proxy

    def get_proxy_url(self, with_auth: bool = False) -> Optional[str]:
        '''获取代理 url'''
        proxy = self.get_proxy()
        if proxy:
            # 从环境变量获取代理认证信息
            proxy_username = os.getenv('PROXY_USERNAME')
            proxy_password = os.getenv('PROXY_PASSWORD')

            if proxy_username and proxy_password and with_auth:
                # 带认证的代理格式，统一使用HTTP协议
                proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy['ip']}:{proxy['port']}"
            else:
                # 不带认证的代理格式
                proxy_url = f"http://{proxy['ip']}:{proxy['port']}"

            return proxy_url

        return None
    
    def trans_proxy_to_url(self, proxy: Dict[str, str], with_auth: bool = False) -> Optional[str]:
        if not proxy: return None
        # 从环境变量获取代理认证信息
        proxy_username = os.getenv('PROXY_USERNAME')   
        proxy_password = os.getenv('PROXY_PASSWORD')

        if proxy_username and proxy_password and with_auth:
            # 带认证的代理格式，统一使用HTTP协议
            proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy['ip']}:{proxy['port']}"
        else:
            # 不带认证的代理格式
            proxy_url = f"http://{proxy['ip']}:{proxy['port']}"

        return proxy_url

class PlaywrightDriver:
    """基于 Playwright 的异步驱动管理器 - 支持真正的并发和自定义代理"""
    
    def __init__(self, max_concurrent: int = 5, headless: bool = True, timeout: int = 10000):
        """
        初始化 Playwright 驱动管理器
        
        Args:
            max_concurrent: 最大并发数
            headless: 是否无头模式
            timeout: 页面超时时间（毫秒）
        """
        self.max_concurrent = max_concurrent
        self.headless = headless
        self.timeout = timeout
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Playwright 实例
        self.playwright = None
        self.browser = None
        self._is_initialized = False
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def start(self):
        """启动 Playwright 浏览器"""
        if not self._is_initialized:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-gpu',
                    '--disable-images',  # 禁用图片加载以提高速度
                    '--disable-javascript',  # 如果不需要JS可以禁用
                ]
            )
            self._is_initialized = True
            logging.info("Playwright 浏览器已启动")
            
    async def close(self):
        """关闭 Playwright 浏览器"""
        if self.browser:
            await self.browser.close()
            self.browser = None
            
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
            
        self._is_initialized = False
        logging.info("Playwright 浏览器已关闭")
        
    def _parse_proxy_url(self, proxy_url: str) -> Dict:
        """
        解析代理 URL，支持带认证的代理
        
        Args:
            proxy_url: 代理URL，格式如 '*********************:port' 或 'host:port'
            
        Returns:
            解析后的代理配置字典
        """
        if not proxy_url:
            return None
            
        proxy_config = {}
        
        # 处理不同格式的代理URL
        if '://' in proxy_url:
            parsed = urlparse(proxy_url)
            proxy_config['server'] = f"{parsed.scheme}://{parsed.hostname}:{parsed.port}"
            if parsed.username and parsed.password:
                proxy_config['username'] = parsed.username
                proxy_config['password'] = parsed.password
        else:
            # 处理 user:pass@host:port 或 host:port 格式
            if '@' in proxy_url:
                auth_part, host_part = proxy_url.split('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
                    proxy_config['username'] = username
                    proxy_config['password'] = password
                    
                if ':' in host_part:
                    host, port = host_part.split(':', 1)
                else:
                    host, port = host_part, '80'
                    
                proxy_config['server'] = f"http://{host}:{port}"
            else:
                # 简单的 host:port 格式
                if ':' in proxy_url:
                    host, port = proxy_url.split(':', 1)
                else:
                    host, port = proxy_url, '80'
                proxy_config['server'] = f"http://{host}:{port}"
                
        return proxy_config
        
    async def _create_context(self, proxy_url: str = None) -> BrowserContext:
        """
        创建浏览器上下文，支持自定义代理
        
        Args:
            proxy_url: 代理URL
            
        Returns:
            BrowserContext 实例
        """
        context_options = {
            'user_agent': self.user_agent,
            'viewport': {'width': 1920, 'height': 1080},
            'ignore_https_errors': True,
        }
        
        # 配置代理
        proxy_config = self._parse_proxy_url(proxy_url)
        if proxy_config:
            context_options['proxy'] = proxy_config
            
        return await self.browser.new_context(**context_options)
        
    async def safe_get(self, url: str, max_retries: int = 3, proxy_url: str = None) -> Optional[str]:
        """
        安全地访问单个URL
        
        Args:
            url: 要访问的URL
            max_retries: 最大重试次数
            proxy_url: 代理URL
            
        Returns:
            页面HTML内容，失败返回None
        """
        if not self._is_initialized:
            await self.start()
            
        async with self.semaphore:  # 限制并发数
            context = None
            page = None
            
            try:
                context = await self._create_context(proxy_url)
                page = await context.new_page()
                
                # 设置超时时间
                # page.set_default_timeout(self.timeout)
                
                for attempt in range(max_retries):
                    try:
                        # 访问页面
                        await page.goto(url, timeout=self.timeout, wait_until='domcontentloaded')
                        
                        # 等待页面完全加载
                        # await page.wait_for_load_state('networkidle', timeout=10000)
                        
                        # 获取页面内容
                        content = await page.content()
                        
                        logging.info(f"成功获取页面: {url}")
                        return content
                        
                    except Exception as e:
                        logging.warning(f"访问URL失败 (尝试 {attempt + 1}/{max_retries}): {url} - {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(random.uniform(1, 3))
                            
                return None
                
            except Exception as e:
                logging.error(f"创建浏览器上下文失败: {url} - {e}")
                return None
                
            finally:
                # 清理资源
                if page:
                    await page.close()
                if context:
                    await context.close()
                    
    async def safe_get_batch(self, 
                           urls: List[str], 
                           max_retries: int = 3, 
                           proxy_urls: Union[str, List[str], None] = None) -> List[Optional[str]]:
        """
        批量并发访问多个URL
        
        Args:
            urls: URL列表
            max_retries: 最大重试次数
            proxy_urls: 代理URL，可以是单个字符串（所有请求使用相同代理）或列表（每个请求使用对应代理）
            
        Returns:
            页面内容列表，与输入URL顺序对应
        """
        if not self._is_initialized:
            await self.start()
            
        # 处理代理URL参数
        if isinstance(proxy_urls, str):
            # 单个代理URL，所有请求使用相同代理
            proxy_list = [proxy_urls] * len(urls)
        elif isinstance(proxy_urls, list):
            # 代理URL列表
            if len(proxy_urls) != len(urls):
                raise ValueError("代理URL列表长度必须与URL列表长度相同")
            proxy_list = proxy_urls
        else:
            # 不使用代理
            proxy_list = [None] * len(urls)
            
        # 创建异步任务
        tasks = [
            self.safe_get(url, max_retries, proxy_url)
            for url, proxy_url in zip(urls, proxy_list)
        ]
        
        # 执行所有任务
        logging.info(f"开始并发访问 {len(urls)} 个URL，最大并发数: {self.max_concurrent}")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logging.error(f"URL {urls[i]} 访问失败: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
                
        success_count = sum(1 for r in processed_results if r is not None)
        logging.info(f"批量访问完成: {success_count}/{len(urls)} 成功")
        
        return processed_results
        
    async def safe_get_with_custom_action(self, 
                                        url: str, 
                                        action_func: callable = None,
                                        max_retries: int = 3, 
                                        proxy_url: str = None) -> Optional[str]:
        """
        访问URL并执行自定义操作
        
        Args:
            url: 要访问的URL
            action_func: 自定义操作函数，接收page对象作为参数
            max_retries: 最大重试次数
            proxy_url: 代理URL
            
        Returns:
            页面HTML内容，失败返回None
        """
        if not self._is_initialized:
            await self.start()
            
        async with self.semaphore:
            context = None
            page = None
            
            try:
                context = await self._create_context(proxy_url)
                page = await context.new_page()
                page.set_default_timeout(self.timeout)
                
                for attempt in range(max_retries):
                    try:
                        await page.goto(url, wait_until='domcontentloaded')
                        
                        # 执行自定义操作
                        if action_func:
                            await action_func(page)
                            
                        await page.wait_for_load_state('networkidle', timeout=10000)
                        content = await page.content()
                        
                        return content
                        
                    except Exception as e:
                        logging.warning(f"访问URL失败 (尝试 {attempt + 1}/{max_retries}): {url} - {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(random.uniform(1, 3))
                            
                return None
                
            except Exception as e:
                logging.error(f"执行自定义操作失败: {url} - {e}")
                return None
                
            finally:
                if page:
                    await page.close()
                if context:
                    await context.close()

async def main():
    # 测试URL列表
    test_urls = [
        "https://dl.acm.org/citation.cfm?id=2093503",
        "https://dl.acm.org/citation.cfm?id=2093490",
        "https://dl.acm.org/citation.cfm?id=2093510",
        "https://dl.acm.org/citation.cfm?id=2093504",
        "https://dl.acm.org/citation.cfm?id=2093505",
    ]

    proxy_fetcher = ProxyPoolManager(pool_size=5)
    proxy_urls = [proxy_fetcher.get_proxy_url(with_auth=True) for _ in range(len(test_urls))]

    # 使用类实例请求
    print("\n=== 使用类实例 ===")
    async with PlaywrightDriver(max_concurrent=3) as driver:
        results = await driver.safe_get_batch(test_urls, proxy_urls=proxy_urls)
        # results = await driver.safe_get_batch(test_urls)
        for i, result in enumerate(results):
            print(f"URL {i+1}: {'成功' if result else '失败'}")
            # 去除掉 result 中的所有标签
            result = re.sub(r'<[^>]*>', '', result)
            result = result.replace('\n', '')
            print(result)

if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
